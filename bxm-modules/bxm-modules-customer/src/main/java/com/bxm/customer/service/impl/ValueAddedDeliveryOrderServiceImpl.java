package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.constants.ValueAddedConstants;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderUpsertReq;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.EmployeeInfo;
import com.bxm.customer.domain.vo.valueAdded.NationalTaxAccountVO;
import com.bxm.customer.domain.vo.valueAdded.PersonalTaxAccountVO;
import com.bxm.customer.domain.vo.valueAdded.SaveStatusReqVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.service.manager.IValueAddedEmployeeUpsertManager;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.customer.service.ValueAddedDeliveryOrderStateMachineManager;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService {


    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private ValueAddedDeliveryOrderStateMachineManager valueAddedDeliveryOrderStateMachineManager;

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    private IValueAddedEmployeeUpsertManager upsertManager;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder upsert(DeliveryOrderUpsertReq orderVO) {
        try {
            log.info("Starting upsert operation for delivery order: {}", orderVO.getCustomerName());

            // 执行基础校验
            validateOrderVO(orderVO);

            // VO转换为DO
            ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
            BeanUtils.copyProperties(orderVO, order);

            // 生成交付单标题
            String title = generateTitle(order);
            order.setTitle(title);

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = findExistingOrder(order);

            ValueAddedDeliveryOrder resultOrder;
            if (existingOrder != null) {
                // 更新现有记录
                updateExistingOrder(existingOrder, order);
                String updatedTitle = generateTitle(existingOrder);
                existingOrder.setTitle(updatedTitle);
                updateById(existingOrder);
                resultOrder = existingOrder;
            } else {
                // 创建新记录
                if (StringUtils.isEmpty(order.getStatus())) {
                    order.setStatus(ValueAddedDeliveryOrderStatus.getDefaultStatus().getCode()); // 使用枚举设置默认状态
                }
                order.setIsDel(false);
                Long userId = SecurityUtils.getUserId();
                order.setCreateUid(userId);
                order.setCreateBy(SecurityUtils.getUsername());
                order.setUpdateBy(SecurityUtils.getUsername());
                save(order);
                log.info("Created new delivery order: {}", order.getDeliveryOrderNo());
                resultOrder = order;
            }

            // 处理国税账号和个税账号信息
            processAccountInformation(orderVO, resultOrder.getDeliveryOrderNo());

            return resultOrder;

        } catch (Exception e) {
            log.error("Failed to upsert delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("保存增值交付单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public DeliveryOrderVO getDeliveryOrderVO(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            log.warn("Delivery order number is empty");
            return null;
        }

        try {
            log.info("Getting delivery order VO for order number: {}", deliveryOrderNo);

            // 获取基础交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                log.warn("Delivery order not found: {}", deliveryOrderNo);
                return null;
            }

            // 构建基础VO对象
            DeliveryOrderVO vo = buildBaseDeliveryOrderVO(order);

            // 获取并设置扩展数据
            setExtendedData(vo, deliveryOrderNo, order.getValueAddedItemTypeId());

            log.info("Successfully built delivery order VO for: {}", deliveryOrderNo);
            return vo;

        } catch (Exception e) {
            log.error("Failed to get delivery order VO for: {}", deliveryOrderNo, e);
            throw new RuntimeException("获取交付单详细信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType) {
        if (customerId == null || valueAddedItemType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getCustomerId, customerId)
                .eq(ValueAddedDeliveryOrder::getValueAddedItemTypeId, valueAddedItemType)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public List<ValueAddedDeliveryOrder> query(DeliveryOrderQuery q) {
        // 构建动态查询条件
        LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);

        // 基础文本/数字条件
        wrapper.like(StringUtils.isNotEmpty(q.getCustomerName()), ValueAddedDeliveryOrder::getCustomerName, q.getCustomerName())
                .eq(StringUtils.isNotEmpty(q.getDeliveryOrderNo()), ValueAddedDeliveryOrder::getDeliveryOrderNo, q.getDeliveryOrderNo())
                .eq(StringUtils.isNotEmpty(q.getCreditCode()), ValueAddedDeliveryOrder::getCreditCode, q.getCreditCode())
                .eq(StringUtils.isNotEmpty(q.getTaxNo()), ValueAddedDeliveryOrder::getTaxNo, q.getTaxNo())
                .eq(q.getTaxpayerType() != null, ValueAddedDeliveryOrder::getTaxpayerType, q.getTaxpayerType())
                .eq(q.getValueAddedItemTypeId() != null, ValueAddedDeliveryOrder::getValueAddedItemTypeId, q.getValueAddedItemTypeId())
                .eq(StringUtils.isNotEmpty(q.getStatus()), ValueAddedDeliveryOrder::getStatus, q.getStatus())
                .eq(q.getInitiateDeptId() != null, ValueAddedDeliveryOrder::getInitiateDeptId, q.getInitiateDeptId())
                .eq(q.getBusinessDeptId() != null, ValueAddedDeliveryOrder::getBusinessDeptId, q.getBusinessDeptId())
                .eq(q.getBusinessTopDeptId() != null, ValueAddedDeliveryOrder::getBusinessTopDeptId, q.getBusinessTopDeptId())
                .eq(q.getCreateUid() != null, ValueAddedDeliveryOrder::getCreateUid, q.getCreateUid());

        // 账期范围查询：查询条件的账期范围与数据库记录的账期范围有交集
        if (q.getAccountingPeriodStart() != null && q.getAccountingPeriodEnd() != null) {
            // 查询账期范围与记录账期范围有交集的记录
            // 条件：查询开始 <= 记录结束 AND 查询结束 >= 记录开始
            Integer queryStart = q.getAccountingPeriodStart();
            Integer queryEnd = q.getAccountingPeriodEnd();
            if (queryStart > queryEnd) {
                // 容错：如果开始>结束，自动交换
                Integer temp = queryStart;
                queryStart = queryEnd;
                queryEnd = temp;
            }
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, queryEnd)
                    .ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, queryStart);
        } else if (q.getAccountingPeriodStart() != null) {
            // 只有开始时间：记录的结束时间 >= 查询开始时间
            wrapper.ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, q.getAccountingPeriodStart());
        } else if (q.getAccountingPeriodEnd() != null) {
            // 只有结束时间：记录的开始时间 <= 查询结束时间
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, q.getAccountingPeriodEnd());
        }

        // DDL 日期范围
        LocalDate ddlStart = q.getDdlStart();
        LocalDate ddlEnd = q.getDdlEnd();
        if (ddlStart != null && ddlEnd != null) {
            if (!ddlEnd.isBefore(ddlStart)) {
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlStart, ddlEnd);
            } else {
                // 容错：若结束早于开始，自动纠正
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlEnd, ddlStart);
            }
        } else if (ddlStart != null) {
            wrapper.ge(ValueAddedDeliveryOrder::getDdl, ddlStart);
        } else if (ddlEnd != null) {
            wrapper.le(ValueAddedDeliveryOrder::getDdl, ddlEnd);
        }

        // 排序：默认创建时间倒序
        wrapper.orderByDesc(ValueAddedDeliveryOrder::getCreateTime);

        // BaseController.startPage() 注入
        return this.list(wrapper);
    }

    @Override
    public List<DeliveryOrderUpsertReq> queryVO(DeliveryOrderQuery q) {
        // 调用现有的query方法获取实体列表
        List<ValueAddedDeliveryOrder> deliveryOrders = query(q);

        if (deliveryOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的itemTypeId
        Set<Integer> itemTypeIds = deliveryOrders.stream()
                .map(ValueAddedDeliveryOrder::getValueAddedItemTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询增值事项类型信息，建立itemTypeId到itemName的映射
        Map<Integer, String> itemTypeIdToNameMap = new HashMap<>();
        if (!itemTypeIds.isEmpty()) {
            try {
                // 批量查询所有需要的增值事项类型
                List<ValueAddedItemType> itemTypes = valueAddedItemTypeService.listByIds(itemTypeIds);

                // 构建映射关系
                for (ValueAddedItemType itemType : itemTypes) {
                    if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName()) && !Boolean.TRUE.equals(itemType.getIsDel())) {
                        itemTypeIdToNameMap.put(itemType.getId().intValue(), itemType.getItemName());
                    }
                }

                log.debug("Successfully loaded {} item type mappings from batch query", itemTypeIdToNameMap.size());
            } catch (Exception e) {
                log.error("Failed to load item type mappings from batch query", e);
                // 继续执行，但itemName将为空
            }
        }

        // 转换实体对象到VO对象
        final Map<Integer, String> finalItemTypeIdToNameMap = itemTypeIdToNameMap;
        return deliveryOrders.stream()
                .map(order -> convertToVO(order, finalItemTypeIdToNameMap))
                .collect(Collectors.toList());
    }

    /**
     * 将实体对象转换为VO对象
     *
     * @param order               增值交付单实体对象
     * @param itemTypeIdToNameMap itemTypeId到itemName的映射
     * @return VO对象
     */
    private DeliveryOrderUpsertReq convertToVO(ValueAddedDeliveryOrder order, Map<Integer, String> itemTypeIdToNameMap) {
        DeliveryOrderUpsertReq vo = new DeliveryOrderUpsertReq();

        // 使用BeanUtils复制基础字段
        BeanUtils.copyProperties(order, vo);

        // 映射itemId到itemName
        if (order.getValueAddedItemTypeId() != null) {
            String itemName = itemTypeIdToNameMap.get(order.getValueAddedItemTypeId());
            vo.setItemName(itemName != null ? itemName : "");
            log.debug("Mapped itemTypeId {} to itemName: {}", order.getValueAddedItemTypeId(), itemName);
        }

        return vo;
    }

    /**
     * 验证订单VO
     */
    private void validateOrderVO(DeliveryOrderUpsertReq orderVO) {
        // 验证交付单编号（必填字段）
        if (StringUtils.isEmpty(orderVO.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 验证状态（如果提供了状态）
        if (StringUtils.isNotEmpty(orderVO.getStatus()) && !ValueAddedDeliveryOrderStatus.isValid(orderVO.getStatus())) {
            throw new IllegalArgumentException("无效的交付状态: " + orderVO.getStatus());
        }

        // 验证纳税性质
        if (orderVO.getTaxpayerType() != null && (orderVO.getTaxpayerType() < 1 || orderVO.getTaxpayerType() > 2)) {
            throw new IllegalArgumentException("无效的纳税性质: " + orderVO.getTaxpayerType());
        }

        // 验证账期逻辑（账期格式为YYYYMM的整数，如：202301）
        if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
            if (orderVO.getAccountingPeriodStart() > orderVO.getAccountingPeriodEnd()) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 查找现有记录
     */
    private ValueAddedDeliveryOrder findExistingOrder(ValueAddedDeliveryOrder order) {
        if (StringUtils.isNotEmpty(order.getDeliveryOrderNo())) {
            return getByDeliveryOrderNo(order.getDeliveryOrderNo());
        }
        return null;
    }

    /**
     * 更新现有记录
     */
    private void updateExistingOrder(ValueAddedDeliveryOrder existingOrder, ValueAddedDeliveryOrder newOrder) {
        // 保留原有的ID、交付单编号和创建相关信息
        Long originalId = existingOrder.getId();
        String originalDeliveryOrderNo = existingOrder.getDeliveryOrderNo();
        String originalCreateBy = existingOrder.getCreateBy();
        Long originalCreateUid = existingOrder.getCreateUid();
        BeanUtils.copyProperties(newOrder, existingOrder);
        // 恢复不应该被更新的字段
        existingOrder.setId(originalId);
        existingOrder.setDeliveryOrderNo(originalDeliveryOrderNo);
        existingOrder.setCreateBy(originalCreateBy);
        existingOrder.setCreateUid(originalCreateUid);
    }

    /**
     * 生成交付单标题
     * 格式：增值交付单-{itemName}-{accountingPeriodStart}-{accountingPeriodEnd}
     *
     * @param order 交付单对象
     * @return 生成的标题
     */
    private String generateTitle(ValueAddedDeliveryOrder order) {
        try {
            // 获取增值事项名称
            String itemName = "未知事项";
            if (order.getValueAddedItemTypeId() != null) {
                ValueAddedItemType itemType = valueAddedItemTypeService.getById(order.getValueAddedItemTypeId());
                if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName())) {
                    itemName = itemType.getItemName();
                }
            }

            // 格式化账期
            String periodStart = order.getAccountingPeriodStart() != null ?
                    String.valueOf(order.getAccountingPeriodStart()) : "未设置";
            String periodEnd = order.getAccountingPeriodEnd() != null ?
                    String.valueOf(order.getAccountingPeriodEnd()) : "未设置";

            // 生成标题
            String title = String.format("增值交付单-%s-%s-%s", itemName, periodStart, periodEnd);

            log.debug("Generated title for delivery order: {}", title);
            return title;

        } catch (Exception e) {
            log.warn("Failed to generate title for delivery order, using default title. Error: {}", e.getMessage());
            return "增值交付单-未知事项-未设置-未设置";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(StatusChangeRequestDTO request) {
        try {
            log.info("Starting transactional status change for order: {} to status: {}", request.getDeliveryOrderNo(), request.getTargetStatus());
            // 1. 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(request.getDeliveryOrderNo());
            if (order == null) {
                throw new IllegalArgumentException("交付单不存在: " + request.getDeliveryOrderNo());
            }

            // 2. 解析当前状态和目标状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

            if (currentStatus == null) {
                throw new IllegalArgumentException("当前状态无效: " + order.getStatus());
            }
            if (targetStatus == null) {
                throw new IllegalArgumentException("目标状态无效: " + request.getTargetStatus());
            }

            // 3. 检查状态是否相同
            if (currentStatus == targetStatus) {
                throw new IllegalArgumentException("当前状态与目标状态相同，无需变更");
            }

            // 4. 状态机管理器处理验证
            valueAddedDeliveryOrderStateMachineManager.validateAndChangeStatus(order, currentStatus, targetStatus, request);

            // 5. 执行数据库更新（在事务中）
            boolean updateResult = updateById(order);
            if (!updateResult) {
                log.error("Database update failed for order: {}, transaction will be rolled back", request.getDeliveryOrderNo());
                throw new RuntimeException("状态更新失败，事务已回滚");
            }

            log.info("Transactional status change completed successfully for order: {} from {} to {}",
                    request.getDeliveryOrderNo(), currentStatus.getDescription(), targetStatus.getDescription());

        } catch (Exception e) {
            log.error("Transactional status change failed for order: {}, transaction rolled back, error: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAvailableStatuses(String deliveryOrderNo) {
        try {
            log.info("Getting available statuses for order: {}", deliveryOrderNo);

            // 参数验证
            if (StringUtils.isEmpty(deliveryOrderNo)) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }

            // 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                log.warn("Delivery order not found: {}", deliveryOrderNo);
                return new ArrayList<>();
            }

            // 解析当前状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (currentStatus == null) {
                log.warn("Invalid current status for order: {}, status: {}", deliveryOrderNo, order.getStatus());
                return new ArrayList<>();
            }

            // 获取可用的下一状态
            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    valueAddedDeliveryOrderStateMachineManager.getAvailableNextStatuses(currentStatus);

            log.info("Found {} available statuses for order: {}", availableStatuses.size(), deliveryOrderNo);
            return availableStatuses;

        } catch (Exception e) {
            log.error("Failed to get available statuses for order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStatus(SaveStatusReqVO request) {
        try {
            log.info("Starting save status operation for order: {}, target status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = getByDeliveryOrderNo(request.getDeliveryOrderNo());
            if (existingOrder == null) {
                throw new IllegalArgumentException("未找到对应的增值交付单: " + request.getDeliveryOrderNo());
            }

            // 验证当前状态是否适用于此操作（待交付、待扣款状态）
            String currentStatus = existingOrder.getStatus();
            if (!isValidStatusForSave(currentStatus)) {
                log.warn("Current status {} is not valid for save status operation", currentStatus);
                throw new IllegalArgumentException("当前状态不支持此操作，仅支持待交付、待扣款状态");
            }

            // 更新处理状态和相关字段
            existingOrder.setProcessingStatus(request.getTargetStatus());

            // 如果提供了总扣缴额，则更新
            if (request.getTotalWithholdingAmount() != null) {
                existingOrder.setTotalWithholdingAmount(request.getTotalWithholdingAmount());
            }


            // 执行数据库更新
            boolean updateResult = updateById(existingOrder);
            if (!updateResult) {
                log.error("Database update failed for order: {}, transaction will be rolled back", request.getDeliveryOrderNo());
                throw new RuntimeException("状态保存失败，事务已回滚");
            }

        } catch (Exception e) {
            log.error("Save status failed for order: {}, error: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 验证当前状态是否适用于保存状态操作
     * 仅支持待交付、待扣款状态
     *
     * @param currentStatus 当前状态
     * @return 是否有效
     */
    private boolean isValidStatusForSave(String currentStatus) {
        if (StringUtils.isEmpty(currentStatus)) {
            return false;
        }

        ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(currentStatus);
        if (status == null) {
            return false;
        }

        // 仅支持待交付、待扣款状态
        return status == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
                status == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }

    /**
     * 构建基础DeliveryOrderVO对象
     *
     * @param order 交付单实体
     * @return DeliveryOrderVO对象
     */
    private DeliveryOrderVO buildBaseDeliveryOrderVO(ValueAddedDeliveryOrder order) {
        DeliveryOrderVO vo = new DeliveryOrderVO();

        // 复制基础字段
        BeanUtils.copyProperties(order, vo);

        // 获取增值事项类型信息
        if (order.getValueAddedItemTypeId() != null) {
            try {
                ValueAddedItemType itemType = valueAddedItemTypeService.getById(order.getValueAddedItemTypeId());
                if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName()) && !Boolean.TRUE.equals(itemType.getIsDel())) {
                    // 设置增值事项名称（保持向后兼容）
                    vo.setItemName(itemType.getItemName());
                    // 设置完整的增值事项类型对象
                    ValueAddedItemTypeVO itemTypeVO = convertToItemTypeVO(itemType);
                    vo.setValueAddedItemType(itemTypeVO);
                }
            } catch (Exception e) {
                log.warn("Failed to get item type info for id: {}", order.getValueAddedItemTypeId(), e);
            }
        }

        return vo;
    }

    /**
     * 设置扩展数据
     *
     * @param vo                   交付单VO
     * @param deliveryOrderNo      交付单编号
     * @param valueAddedItemTypeId 增值事项类型ID
     */
    private void setExtendedData(DeliveryOrderVO vo, String deliveryOrderNo, Integer valueAddedItemTypeId) {
        try {
            // 获取国税账号对象（bizType=3）
            vo.setNationalTaxAccount(getNationalTaxAccount(deliveryOrderNo));

            // 获取个税账号对象（bizType=4）
            vo.setPersonalTaxAccount(getPersonalTaxAccount(deliveryOrderNo));

            // 根据itemCode获取员工信息列表
            vo.setEmployeeInfoList(getEmployeeInfoList(deliveryOrderNo, valueAddedItemTypeId));

            // 获取交付文件列表（fileType=1）
            vo.setDeliveryFiles(getDeliveryFiles(deliveryOrderNo));

        } catch (Exception e) {
            log.error("Failed to set extended data for delivery order: {}", deliveryOrderNo, e);
            // 设置默认值，避免返回null
            vo.setEmployeeInfoList(new ArrayList<>());
            vo.setDeliveryFiles(new ArrayList<>());
        }
    }

    /**
     * 获取国税账号对象（bizType=3）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 国税账号VO对象，如果不存在则返回null
     */
    private NationalTaxAccountVO getNationalTaxAccount(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, ValueAddedConstants.BizType.NATIONAL_TAX_ACCOUNT)
                    .last("LIMIT 1");

            ValueAddedEmployee employee = valueAddedEmployeeService.getOne(wrapper);
            return employee != null ? convertToNationalTaxAccountVO(employee) : null;
        } catch (Exception e) {
            log.error("Failed to get national tax account for order: {}", deliveryOrderNo, e);
            return null;
        }
    }

    /**
     * 获取个税账号对象（bizType=4）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 个税账号VO对象，如果不存在则返回null
     */
    private PersonalTaxAccountVO getPersonalTaxAccount(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, ValueAddedConstants.BizType.PERSONAL_TAX_ACCOUNT)
                    .last("LIMIT 1");

            ValueAddedEmployee employee = valueAddedEmployeeService.getOne(wrapper);
            return employee != null ? convertToPersonalTaxAccountVO(employee) : null;
        } catch (Exception e) {
            log.error("Failed to get personal tax account for order: {}", deliveryOrderNo, e);
            return null;
        }
    }

    /**
     * 根据itemCode获取员工信息列表
     * 当itemCode为TAX_SOCIAL_INSURANCE时bizType对应1，TAX_PERSONAL_SALARY对应bizType为2，最多查50条
     *
     * @param deliveryOrderNo      交付单编号
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return 员工信息列表
     */
    private List<EmployeeInfo> getEmployeeInfoList(String deliveryOrderNo, Integer valueAddedItemTypeId) {
        try {
            // 根据增值事项类型ID确定bizType
            Integer bizType = determineBizTypeByItemTypeId(valueAddedItemTypeId);
            if (bizType == null) {
                log.debug("No matching bizType for itemTypeId: {}", valueAddedItemTypeId);
                return new ArrayList<>();
            }

            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, bizType)
                    .last("LIMIT " + ValueAddedConstants.EmployeeLimit.MAX_QUERY_SIZE);

            List<ValueAddedEmployee> employees = valueAddedEmployeeService.list(wrapper);

            // 转换为EmployeeInfo列表
            return employees.stream()
                    .map(this::convertToEmployeeInfo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to get employee info list for order: {}", deliveryOrderNo, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据增值事项类型ID确定bizType
     *
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return bizType，如果无匹配则返回null
     */
    private Integer determineBizTypeByItemTypeId(Integer valueAddedItemTypeId) {
        if (valueAddedItemTypeId == null) {
            return null;
        }

        // 根据增值事项类型ID映射到bizType
        if (ValueAddedConstants.ItemTypeId.SOCIAL_INSURANCE.equals(valueAddedItemTypeId)) {
            return ValueAddedConstants.BizType.SOCIAL_INSURANCE; // 社医保
        } else if (ValueAddedConstants.ItemTypeId.PERSONAL_TAX.equals(valueAddedItemTypeId)) {
            return ValueAddedConstants.BizType.PERSONAL_TAX; // 个税明细
        }

        return null;
    }

    /**
     * 将ValueAddedEmployee转换为EmployeeInfo
     *
     * @param employee 员工实体
     * @return EmployeeInfo对象
     */
    private EmployeeInfo convertToEmployeeInfo(ValueAddedEmployee employee) {
        return EmployeeInfo.builder()
                .operationType(employee.getOperationType())
                .employeeName(employee.getEmployeeName())
                .idNumber(employee.getIdNumber())
                .mobile(employee.getMobile())
                .remark(employee.getRemark())
                .grossSalary(employee.getGrossSalary())
                .providentFundPersonal(employee.getProvidentFundPersonal())
                .socialInsuranceBase(employee.getSocialInsuranceBase())
                .socialInsurance(employee.getSocialInsurance())
                .build();
    }

    /**
     * 获取交付文件列表（fileType=1）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 文件URL列表
     */
    private List<String> getDeliveryFiles(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedFile> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedFile::getFileType, ValueAddedConstants.FileType.DELIVERY_MATERIAL)
                    .eq(ValueAddedFile::getIsDel, false);

            List<ValueAddedFile> files = valueAddedFileService.list(wrapper);

            return files.stream()
                    .map(ValueAddedFile::getFileUrl)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to get delivery files for order: {}", deliveryOrderNo, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将ValueAddedEmployee转换为NationalTaxAccountVO
     *
     * @param employee 员工实体
     * @return 国税账号VO
     */
    private NationalTaxAccountVO convertToNationalTaxAccountVO(ValueAddedEmployee employee) {
        return NationalTaxAccountVO.builder()
                .id(employee.getId())
                .accountNumber(employee.getTaxNumber())
                .password(employee.getQueryPassword())
                .loginMethod(employee.getLoginMethod())
                .realNameAgent(employee.getRealNameAgent())
                .mobile(employee.getMobile())
                .idNumber(employee.getIdNumber())
                .remark(employee.getRemark())
                .operationType(employee.getOperationType())
                .build();
    }

    /**
     * 将ValueAddedEmployee转换为PersonalTaxAccountVO
     *
     * @param employee 员工实体
     * @return 个税账号VO
     */
    private PersonalTaxAccountVO convertToPersonalTaxAccountVO(ValueAddedEmployee employee) {
        return PersonalTaxAccountVO.builder()
                .id(employee.getId())
                .accountNumber(employee.getTaxNumber())
                .password(employee.getQueryPassword())
                .loginMethod(employee.getLoginMethod())
                .realNameAgent(employee.getRealNameAgent())
                .mobile(employee.getMobile())
                .idNumber(employee.getIdNumber())
                .remark(employee.getRemark())
                .operationType(employee.getOperationType())
                .build();
    }

    /**
     * 处理账号信息（国税账号和个税账号）
     *
     * @param orderVO         交付单请求VO
     * @param deliveryOrderNo 交付单编号
     */
    private void processAccountInformation(DeliveryOrderUpsertReq orderVO, String deliveryOrderNo) {
        try {
            // 处理国税账号
            if (orderVO.getNationalTaxAccount() != null) {
                processNationalTaxAccount(orderVO.getNationalTaxAccount(), deliveryOrderNo);
            }

            // 处理个税账号
            if (orderVO.getPersonalTaxAccount() != null) {
                processPersonalTaxAccount(orderVO.getPersonalTaxAccount(), deliveryOrderNo);
            }
        } catch (Exception e) {
            log.error("Failed to process account information for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理账号信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理国税账号信息
     *
     * @param nationalTaxAccountVO 国税账号VO
     * @param deliveryOrderNo      交付单编号
     */
    private void processNationalTaxAccount(NationalTaxAccountVO nationalTaxAccountVO, String deliveryOrderNo) {
        try {
            log.info("Processing national tax account for delivery order: {}", deliveryOrderNo);
            // 转换为ValueAddedEmployeeVO
            ValueAddedEmployeeVO employeeVO = convertNationalTaxAccountToEmployeeVO(nationalTaxAccountVO, deliveryOrderNo);
            // 调用UpsertManager的upsert方法
            Long employeeId = upsertManager.upsert(employeeVO);
            log.info("National tax account processed successfully for delivery order: {}, employee ID: {}",
                    deliveryOrderNo, employeeId);
        } catch (Exception e) {
            log.error("Failed to process national tax account for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理国税账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理个税账号信息
     *
     * @param personalTaxAccountVO 个税账号VO
     * @param deliveryOrderNo      交付单编号
     */
    private void processPersonalTaxAccount(PersonalTaxAccountVO personalTaxAccountVO, String deliveryOrderNo) {
        try {
            log.info("Processing personal tax account for delivery order: {}", deliveryOrderNo);
            // 转换为ValueAddedEmployeeVO
            ValueAddedEmployeeVO employeeVO = convertPersonalTaxAccountToEmployeeVO(personalTaxAccountVO, deliveryOrderNo);
            // 调用UpsertManager的upsert方法
            Long employeeId = upsertManager.upsert(employeeVO);

            log.info("Personal tax account processed successfully for delivery order: {}, employee ID: {}",
                    deliveryOrderNo, employeeId);
        } catch (Exception e) {
            log.error("Failed to process personal tax account for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理个税账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建基础的ValueAddedEmployeeVO对象
     *
     * @param deliveryOrderNo     交付单编号
     * @param bizType             业务类型
     * @param defaultEmployeeName 默认员工姓名
     * @return 基础的ValueAddedEmployeeVO对象
     */
    private ValueAddedEmployeeVO buildBaseEmployeeVO(String deliveryOrderNo, Integer bizType, String defaultEmployeeName) {
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);
        employeeVO.setBizType(bizType);
        employeeVO.setEntryType(2); // 单个新增
        employeeVO.setEmployeeName(defaultEmployeeName);
        return employeeVO;
    }

    /**
     * 将国税账号VO转换为ValueAddedEmployeeVO
     *
     * @param nationalTaxAccountVO 国税账号VO
     * @param deliveryOrderNo      交付单编号
     * @return ValueAddedEmployeeVO
     */
    private ValueAddedEmployeeVO convertNationalTaxAccountToEmployeeVO(NationalTaxAccountVO nationalTaxAccountVO, String deliveryOrderNo) {
        // 构建基础EmployeeVO对象
        String defaultEmployeeName = StringUtils.isNotEmpty(nationalTaxAccountVO.getRealNameAgent())
                ? nationalTaxAccountVO.getRealNameAgent() : "国税账号经办人";
        ValueAddedEmployeeVO employeeVO = buildBaseEmployeeVO(deliveryOrderNo,
                ValueAddedConstants.BizType.NATIONAL_TAX_ACCOUNT, defaultEmployeeName);
        BeanUtils.copyProperties(nationalTaxAccountVO, employeeVO);
        // 处理字段名不匹配的特殊映射
        employeeVO.setTaxNumber(nationalTaxAccountVO.getAccountNumber()); // accountNumber → taxNumber
        employeeVO.setQueryPassword(nationalTaxAccountVO.getPassword()); // password → queryPassword
        // 设置操作方式默认值（如果为空）
        if (employeeVO.getOperationType() == null) {
            employeeVO.setOperationType(1); // 默认为会计实名
        }

        return employeeVO;
    }

    /**
     * 将个税账号VO转换为ValueAddedEmployeeVO
     *
     * @param personalTaxAccountVO 个税账号VO
     * @param deliveryOrderNo      交付单编号
     * @return ValueAddedEmployeeVO
     */
    private ValueAddedEmployeeVO convertPersonalTaxAccountToEmployeeVO(PersonalTaxAccountVO personalTaxAccountVO, String deliveryOrderNo) {
        // 构建基础EmployeeVO对象
        String defaultEmployeeName = StringUtils.isNotEmpty(personalTaxAccountVO.getRealNameAgent())
                ? personalTaxAccountVO.getRealNameAgent() : "个税账号经办人";
        ValueAddedEmployeeVO employeeVO = buildBaseEmployeeVO(deliveryOrderNo,
                ValueAddedConstants.BizType.PERSONAL_TAX_ACCOUNT, defaultEmployeeName);

        BeanUtils.copyProperties(personalTaxAccountVO, employeeVO);
        employeeVO.setTaxNumber(personalTaxAccountVO.getAccountNumber()); // accountNumber → taxNumber
        employeeVO.setQueryPassword(personalTaxAccountVO.getPassword()); // password → queryPassword

        // 设置操作方式默认值（如果为空）
        if (employeeVO.getOperationType() == null) {
            employeeVO.setOperationType(1);
        }
        return employeeVO;
    }


    /**
     * 将 ValueAddedItemType 实体转换为 ValueAddedItemTypeVO
     *
     * @param itemType 增值事项类型实体
     * @return 增值事项类型VO对象
     */
    private ValueAddedItemTypeVO convertToItemTypeVO(ValueAddedItemType itemType) {
        if (itemType == null) {
            return null;
        }
        ValueAddedItemTypeVO vo = new ValueAddedItemTypeVO();
        BeanUtils.copyProperties(itemType, vo);
        return vo;
    }


}
