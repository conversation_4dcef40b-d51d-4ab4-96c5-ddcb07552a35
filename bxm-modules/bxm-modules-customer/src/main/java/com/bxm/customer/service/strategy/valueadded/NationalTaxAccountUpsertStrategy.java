package com.bxm.customer.service.strategy.valueadded;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 国税账号业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class NationalTaxAccountUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 验证操作类型是否适用于国税账号业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.NATIONAL_TAX_ACCOUNT, employee.getOperationType())) {
            throw new IllegalArgumentException("国税账号业务不支持的操作类型: " + employee.getOperationType());
        }

        // 验证国税账号业务必填字段：登录方式、账号、登录密码
        if (StringUtils.isEmpty(employee.getLoginMethod())) {
            throw new IllegalArgumentException("国税账号的登录方式不能为空");
        }

        if (StringUtils.isEmpty(employee.getTaxNumber())) {
            throw new IllegalArgumentException("国税账号的账号不能为空");
        }

        if (StringUtils.isEmpty(employee.getQueryPassword())) {
            throw new IllegalArgumentException("国税账号的登录密码不能为空");
        }

    }

    @Override
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 标准化税号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getTaxNumber())) {
            employee.setTaxNumber(employee.getTaxNumber().trim().toUpperCase());
        }

        // 构建扩展信息
        ValueAddedOperationType operationType = getOperationTypeByCode(employee.getOperationType());
        if (operationType != null) {
            Map<String, Object> bizTypeExtendInfo = buildBizTypeExtendInfo(
                    ValueAddedBizType.NATIONAL_TAX_ACCOUNT,
                    operationType
            );
            employee.setExtendInfo(mergeExtendInfo(employee.getExtendInfo(), bizTypeExtendInfo));
        }

        log.info("National tax account employee preprocessed: Name={}, TaxNumber={}, OperationType={}",
                employee.getEmployeeName(), maskSensitiveInfo(employee.getTaxNumber()),
                operationType != null ? operationType.getName() : "Unknown");
    }



    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 国税账号业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        return findExistingEmployeeByIdNumber(employee, ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode());
    }

    @Override
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 国税账号特定字段的非空值复制
        if (StringUtils.isNotEmpty(newEmployee.getTaxNumber())) {
            existing.setTaxNumber(newEmployee.getTaxNumber());
        }
        if (StringUtils.isNotEmpty(newEmployee.getQueryPassword())) {
            existing.setQueryPassword(newEmployee.getQueryPassword());
        }
        if (newEmployee.getLoginMethod() != null) {
            existing.setLoginMethod(newEmployee.getLoginMethod());
        }

        log.info("National tax account employee merged: ID={}, Name={}, TaxNumber={}",
                existing.getId(), existing.getEmployeeName(), maskSensitiveInfo(existing.getTaxNumber()));
    }




    /**
     * 根据操作类型代码获取对应的枚举
     *
     * @param operationTypeCode 操作类型代码
     * @return 操作类型枚举
     */
    private ValueAddedOperationType getOperationTypeByCode(Integer operationTypeCode) {
        if (operationTypeCode == null) {
            return null;
        }

        switch (operationTypeCode) {
            case 1:
                return ValueAddedOperationType.ACCOUNTING_REAL_NAME;
            case 2:
                return ValueAddedOperationType.REMOTE_REAL_NAME;
            default:
                return null;
        }
    }

    /**
     * 根据操作类型代码获取中文名称
     *
     * @param operationType 操作类型代码
     * @return 操作类型中文名称
     */
    private String getOperationTypeName(Integer operationType) {
        ValueAddedOperationType operationTypeEnum = getOperationTypeByCode(operationType);
        if (operationTypeEnum != null) {
            return operationTypeEnum.getName();
        }

        return operationType != null ? "未知操作(" + operationType + ")" : "未知操作";
    }

    /**
     * 脱敏敏感信息用于日志输出
     *
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (StringUtils.isEmpty(sensitiveInfo)) {
            return sensitiveInfo;
        }

        if (sensitiveInfo.length() <= 4) {
            return "****";
        }

        // 保留前2位和后2位，中间用*替代
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }

}
