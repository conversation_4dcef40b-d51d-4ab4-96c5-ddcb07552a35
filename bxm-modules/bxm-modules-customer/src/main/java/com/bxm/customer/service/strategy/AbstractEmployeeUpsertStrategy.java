package com.bxm.customer.service.strategy;

import com.alibaba.fastjson2.JSON;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.IValueAddedEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 增值员工upsert策略抽象基类
 *
 * 提供通用的字段清理和默认实现，减少子类的重复代码。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
public abstract class AbstractEmployeeUpsertStrategy implements ValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmpValidationHelper valueAddedEmpValidationHelper;

    @Autowired
    protected IValueAddedEmployeeService valueAddedEmployeeService;

    @Override
    public void preprocessEmployee(ValueAddedEmployee employee) {
        // 设置业务类型
        employee.setBizType(getSupportedBizType());

        // 根据业务类型清理不相关字段
        cleanupIrrelevantFields(employee);

        // 执行业务特定的预处理
        doPreprocess(employee);
    }


    @Override
    public ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 使用BeanUtils复制基础字段，但排除ID和创建时间等不应被覆盖的字段
        String[] ignoreProperties = {"id", "createTime", "createBy"};
        BeanUtils.copyProperties(newEmployee, existing, ignoreProperties);

        // 执行业务特定的合并逻辑
        doMerge(existing, newEmployee);

        return existing;
    }

    /**
     * 根据业务类型清理不相关字段
     *
     * @param employee 员工信息
     */
    protected void cleanupIrrelevantFields(ValueAddedEmployee employee) {
        ValueAddedBizType bizType = ValueAddedBizType.getByCode(getSupportedBizType());

        switch (bizType) {
            case SOCIAL_INSURANCE:
                // 社医保业务：清理国税账号专用字段
                employee.setTaxNumber(null);
                employee.setQueryPassword(null);
                break;
            case PERSONAL_TAX:
                // 个税明细业务：清理其他业务专用字段，保留社保信息字段
                employee.setTaxNumber(null);
                employee.setQueryPassword(null);
                // 个税明细业务也使用 socialInsurance 字段，不再清理
                break;
            case NATIONAL_TAX_ACCOUNT:
                // 国税账号业务：清理其他业务专用字段
                employee.setSocialInsurance(null);
                employee.setGrossSalary(null);
                employee.setProvidentFundPersonal(null);
                break;
            case PERSONAL_TAX_ACCOUNT:
                // 个税账号业务：清理其他业务专用字段
                employee.setSocialInsurance(null);
                employee.setGrossSalary(null);
                employee.setProvidentFundPersonal(null);
                break;
            default:
                // 未知业务类型，不做处理
                break;
        }
    }


    /**
     * 执行业务特定的预处理
     * 子类可以重写此方法实现特定的预处理逻辑
     *
     * @param employee 员工信息
     */
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 默认空实现，子类可以重写
    }



    /**
     * 执行业务特定的合并逻辑
     * 子类可以重写此方法实现特定的字段合并逻辑
     *
     * @param existing 现有员工记录
     * @param newEmployee 新的员工信息
     */
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 默认空实现，子类可以重写
    }

    /**
     * 构建业务类型的扩展信息
     *
     * @param bizType 业务类型枚举
     * @param operationType 操作类型枚举
     * @return 扩展信息Map
     */
    protected Map<String, Object> buildBizTypeExtendInfo(ValueAddedBizType bizType, ValueAddedOperationType operationType) {
        Map<String, Object> extendInfo = new HashMap<>();
        extendInfo.put("accountType", bizType.getName());
        extendInfo.put("businessType", operationType.getName());
        return extendInfo;
    }

    /**
     * 合并扩展信息到现有的扩展信息中
     *
     * @param existingExtendInfo 现有的扩展信息JSON字符串
     * @param newExtendInfo 新的扩展信息Map
     * @return 合并后的扩展信息JSON字符串
     */
    protected String mergeExtendInfo(String existingExtendInfo, Map<String, Object> newExtendInfo) {
        Map<String, Object> extendInfo = parseExtendInfo(existingExtendInfo);
        extendInfo.putAll(newExtendInfo);
        return JSON.toJSONString(extendInfo);
    }

    /**
     * 解析扩展信息JSON字符串为Map
     *
     * @param extendInfoJson 扩展信息JSON字符串
     * @return 扩展信息Map
     */
    protected Map<String, Object> parseExtendInfo(String extendInfoJson) {
        if (StringUtils.isEmpty(extendInfoJson)) {
            return new HashMap<>();
        }
        try {
            return JSON.parseObject(extendInfoJson, Map.class);
        } catch (Exception e) {
            log.warn("Failed to parse extend info JSON: {}, using empty map", extendInfoJson);
            return new HashMap<>();
        }
    }

    /**
     * 校验同一交付单内的唯一性
     *
     * @param employee 员工信息
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    protected void validateUniquenessInDeliveryOrder(ValueAddedEmployee employee) {
        // 校验身份证号唯一性
        if (!valueAddedEmpValidationHelper.checkIdNumberUniquenessInDeliveryOrder(
                employee.getDeliveryOrderNo(), employee.getIdNumber(),
                employee.getBizType(), employee.getId())) {
            throw new IllegalArgumentException("同一交付单内身份证号不能重复");
        }

        // 校验手机号唯一性
        if (!valueAddedEmpValidationHelper.checkMobileUniquenessInDeliveryOrder(
                employee.getDeliveryOrderNo(), employee.getMobile(),
                employee.getBizType(), employee.getId())) {
            throw new IllegalArgumentException("同一交付单内手机号不能重复");
        }
    }

    /**
     * 校验操作类型相关的前置条件
     *
     * @param employee 员工信息
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    protected void validateOperationTypeConditions(ValueAddedEmployee employee) {
        // 校验申报基数前置条件
        ValueAddedEmpValidationHelper.validateSocialInsuranceBaseCondition(
                employee.getOperationType(), employee.getSocialInsuranceBase());
    }

    /**
     * 判断操作类型是否为增员或更正
     *
     * @param operationType 操作类型
     * @return 是否为增员或更正操作
     */
    protected boolean isAddOrCorrectionOperation(Integer operationType) {
        return ValueAddedEmpValidationHelper.isAddOrCorrectionOperation(operationType);
    }

    /**
     * 通用的根据交付单编号和身份证号查找现有员工记录的方法
     * 子类可以直接调用此方法，传入对应的业务类型
     *
     * @param employee 员工信息（包含交付单编号和身份证号）
     * @param bizType 业务类型
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected ValueAddedEmployee findExistingEmployeeByIdNumber(ValueAddedEmployee employee, Integer bizType) {
        return valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                employee.getDeliveryOrderNo(),
                employee.getIdNumber(),
                bizType
        );
    }


}
