package com.bxm.customer.service.manager.impl;

import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.service.manager.IValueAddedEmployeeUpsertManager;
import com.bxm.customer.service.strategy.ValueAddedEmployeeStrategyFactory;
import com.bxm.customer.service.strategy.ValueAddedEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 增值员工信息 Upsert 管理器实现类
 *
 * 专门负责增值员工信息的新增和更新操作，解决循环依赖问题。
 * 将原本在 Service 层的 upsert 逻辑抽取到独立的 Manager 层。
 *
 * 依赖关系：
 * - IValueAddedEmployeeService：用于基础 CRUD 操作和验证
 * - ValueAddedEmployeeStrategyFactory：用于获取业务策略
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedEmployeeUpsertManagerImpl implements IValueAddedEmployeeUpsertManager {

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    private ValueAddedEmployeeStrategyFactory strategyFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upsert(@Valid @NotNull ValueAddedEmployeeVO employeeVO) {
        try {
            log.info("Starting upsert operation for employee: {}, bizType: {}", 
                    employeeVO.getEmployeeName(), employeeVO.getBizType());

            // 1. 基础验证（委托给 Service）
            valueAddedEmployeeService.validateEmployee(employeeVO);

            // 2. VO转换为DO
            ValueAddedEmployee employee = new ValueAddedEmployee();
            if (employeeVO != null) {
                BeanUtils.copyProperties(employeeVO, employee);
            }

            // 3. 获取对应的业务策略
            ValueAddedEmployeeUpsertStrategy strategy = strategyFactory.getStrategy(employee.getBizType());

            // 4. 业务特定字段验证
            strategy.validateBusinessFields(employee);

            // 5. 预处理
            strategy.preprocessEmployee(employee);

            // 6. 查找现有记录
            ValueAddedEmployee existingEmployee = strategy.findExistingEmployee(employee);

            boolean isUpdate = existingEmployee != null;
            ValueAddedEmployee targetEmployee;

            if (isUpdate) {
                // 更新操作：合并数据
                targetEmployee = strategy.mergeEmployee(existingEmployee, employee);

                // 执行更新（委托给 Service）
                if (!valueAddedEmployeeService.updateById(targetEmployee)) {
                    throw new RuntimeException("Failed to update employee: " + targetEmployee.getId());
                }
                log.info("Employee updated successfully: ID={}, Name={}", 
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            } else {
                // 插入操作
                targetEmployee = employee;
                // 执行插入（委托给 Service）
                if (!valueAddedEmployeeService.save(targetEmployee)) {
                    throw new RuntimeException("Failed to insert employee: " + targetEmployee.getEmployeeName());
                }
                log.info("Employee inserted successfully: ID={}, Name={}", 
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            }

            // 返回员工ID
            return targetEmployee.getId();

        } catch (IllegalArgumentException e) {
            log.error("Validation failed during upsert operation for employee: {}, bizType: {}, error: {}", 
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("Runtime error during upsert operation for employee: {}, bizType: {}, error: {}", 
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during upsert operation for employee: {}, bizType: {}, error: {}", 
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage(), e);
            throw new RuntimeException("Upsert operation failed for employee " + employeeVO.getEmployeeName() + ": " + e.getMessage(), e);
        }
    }
}
