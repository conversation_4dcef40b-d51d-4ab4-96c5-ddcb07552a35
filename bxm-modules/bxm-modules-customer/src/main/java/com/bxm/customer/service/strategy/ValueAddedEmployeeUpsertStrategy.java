package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedEmployee;

/**
 * 增值员工upsert策略接口
 * 定义不同业务类型的upsert处理策略
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ValueAddedEmployeeUpsertStrategy {

    /**
     * 获取策略支持的业务类型
     *
     * @return 业务类型代码
     */
    Integer getSupportedBizType();

    /**
     * 验证员工信息的业务特定字段
     *
     * @param employee 员工信息
     * @throws IllegalArgumentException 当验证失败时抛出
     */
    void validateBusinessFields(ValueAddedEmployee employee);

    /**
     * 预处理员工信息
     * 在保存前对特定业务类型的数据进行预处理
     *
     * @param employee 员工信息
     */
    void preprocessEmployee(ValueAddedEmployee employee);



    /**
     * 构建查询条件用于判断记录是否存在
     * 不同业务类型可能有不同的唯一性判断逻辑
     *
     * @param employee 员工信息
     * @return 现有的员工记录，如果不存在则返回null
     */
    ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee);

    /**
     * 合并员工信息
     * 将新的员工信息合并到现有记录中
     *
     * @param existing 现有员工记录
     * @param newEmployee 新的员工信息
     * @return 合并后的员工信息
     */
    ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee);
}
