package com.bxm.customer.service.manager;

import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 增值员工信息 Upsert 管理器接口
 *
 * 专门负责增值员工信息的新增和更新操作，解决循环依赖问题。
 * 支持三种业务类型的员工信息管理：
 * 1. 社医保（bizType=1）：支持提醒、更正、减员操作
 * 2. 个税明细（bizType=2）：支持提醒、更正、减员操作
 * 3. 国税账号（bizType=3）：支持会计实名、异地实名操作
 *
 * 架构设计：
 * Controller -> UpsertManager -> Strategy -> Service (基础方法)
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedEmployeeUpsertManager {

    /**
     * 单条upsert增值员工信息
     *
     * 支持的业务类型：
     * 1. 社医保（bizType=1）：验证社保套餐信息、应发工资等
     * 2. 个税明细（bizType=2）：验证身份证号格式、应发工资等
     * 3. 国税账号（bizType=3）：验证税号格式、查询密码等
     *
     * 处理流程：
     * 1. 基础验证（委托给 Service）
     * 2. VO 转换为 DO
     * 3. 获取对应的业务策略
     * 4. 业务特定字段验证
     * 5. 预处理
     * 6. 查找现有记录
     * 7. 执行新增或更新操作
     *
     * @param employeeVO 增值员工信息VO对象，包含完整的验证注解和业务字段
     * @return 员工ID，新增或更新成功后返回员工的主键ID
     * @throws IllegalArgumentException 当参数验证失败时抛出（如必填字段为空、格式不正确等）
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    Long upsert(@Valid @NotNull ValueAddedEmployeeVO employeeVO);
}
